[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "fifa-ai-agent"
version = "1.0.0"
description = "Professional AI Agent for FIFA 23 Real-Time Gameplay"
authors = [
    {name = "FIFA AI Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
keywords = ["ai", "gaming", "fifa", "computer-vision", "reinforcement-learning"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Games/Entertainment",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    "torch>=2.0.0",
    "ultralytics>=8.0.0",
    "stable-baselines3>=2.0.0",
    "opencv-python>=4.8.0",
    "mss>=9.0.0",
    "pyautogui>=0.9.54",
    "pynput>=1.7.6",
    "pydantic>=2.0.0",
    "PyYAML>=6.0",
    "loguru>=0.7.0",
    "numpy>=1.24.0",
    "matplotlib>=3.7.0",
    "pygame>=2.5.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]
gpu = [
    "cupy-cuda12x>=12.0.0",
]
monitoring = [
    "wandb>=0.15.0",
    "plotly>=5.15.0",
]

[project.urls]
Homepage = "https://github.com/fifa-ai/fifa-ai-agent"
Repository = "https://github.com/fifa-ai/fifa-ai-agent"
Documentation = "https://fifa-ai.readthedocs.io"
"Bug Tracker" = "https://github.com/fifa-ai/fifa-ai-agent/issues"

[project.scripts]
fifa-ai = "fifa_ai_agent.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["fifa_ai_agent*"]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=fifa_ai_agent",
    "--cov-report=term-missing",
    "--cov-report=html",
]

[tool.coverage.run]
source = ["fifa_ai_agent"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
]
