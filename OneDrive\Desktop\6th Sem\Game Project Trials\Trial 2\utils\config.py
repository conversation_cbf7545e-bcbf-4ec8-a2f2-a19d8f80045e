"""
Configuration management system for FIFA AI Agent.

Provides centralized configuration with validation, type safety, and environment support.
"""

import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import yaml
from pydantic import BaseModel, Field, validator


class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: str = Field(default="INFO", description="Logging level")
    file_path: Optional[str] = Field(default=None, description="Log file path")
    max_file_size: str = Field(default="10MB", description="Maximum log file size")
    backup_count: int = Field(default=5, description="Number of backup log files")
    format: str = Field(
        default="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        description="Log message format"
    )


class VisionConfig(BaseModel):
    """Computer vision configuration."""
    capture_fps: int = Field(default=60, description="Screen capture FPS")
    detection_confidence: float = Field(default=0.5, description="YOLO detection confidence threshold")
    nms_threshold: float = Field(default=0.4, description="Non-maximum suppression threshold")
    input_resolution: tuple[int, int] = Field(default=(1920, 1080), description="Expected screen resolution")
    roi_enabled: bool = Field(default=True, description="Enable region of interest optimization")
    roi_coordinates: tuple[int, int, int, int] = Field(
        default=(0, 0, 1920, 1080), 
        description="ROI coordinates (x, y, width, height)"
    )


class AIConfig(BaseModel):
    """AI agent configuration."""
    model_path: str = Field(default="models/policy_agent.pt", description="Path to trained RL model")
    strategy_mode: str = Field(default="adaptive", description="Strategy mode: aggressive, defensive, adaptive")
    decision_frequency: float = Field(default=0.1, description="Decision making frequency in seconds")
    exploration_rate: float = Field(default=0.1, description="Exploration rate for RL agent")
    memory_size: int = Field(default=10000, description="Experience replay memory size")
    batch_size: int = Field(default=32, description="Training batch size")


class InputConfig(BaseModel):
    """Input control configuration."""
    key_delay: float = Field(default=0.05, description="Delay between key presses")
    mouse_speed: float = Field(default=1.0, description="Mouse movement speed multiplier")
    safety_enabled: bool = Field(default=True, description="Enable input safety mechanisms")
    emergency_stop_key: str = Field(default="F12", description="Emergency stop key")


class GameConfig(BaseModel):
    """Game-specific configuration."""
    preferred_team: Optional[str] = Field(default=None, description="Preferred team name")
    difficulty: str = Field(default="Professional", description="Game difficulty")
    match_length: str = Field(default="6 minutes", description="Match length setting")
    auto_team_selection: bool = Field(default=True, description="Enable automatic team selection")
    formation: str = Field(default="4-3-3", description="Preferred formation")


class PerformanceConfig(BaseModel):
    """Performance monitoring configuration."""
    enabled: bool = Field(default=True, description="Enable performance monitoring")
    metrics_interval: float = Field(default=1.0, description="Metrics collection interval")
    save_reports: bool = Field(default=True, description="Save performance reports")
    report_directory: str = Field(default="reports", description="Performance reports directory")


class VisualizationConfig(BaseModel):
    """Visualization dashboard configuration."""
    enabled: bool = Field(default=True, description="Enable visualization dashboard")
    port: int = Field(default=8080, description="Dashboard web server port")
    update_frequency: float = Field(default=0.5, description="Dashboard update frequency")
    show_heatmap: bool = Field(default=True, description="Show decision heatmap")
    show_metrics: bool = Field(default=True, description="Show performance metrics")


class Config(BaseModel):
    """Main configuration class for FIFA AI Agent."""
    
    # Component configurations
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    vision: VisionConfig = Field(default_factory=VisionConfig)
    ai: AIConfig = Field(default_factory=AIConfig)
    input: InputConfig = Field(default_factory=InputConfig)
    game: GameConfig = Field(default_factory=GameConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    visualization: VisualizationConfig = Field(default_factory=VisualizationConfig)
    
    # Global settings
    debug_mode: bool = Field(default=False, description="Enable debug mode")
    profile_performance: bool = Field(default=False, description="Enable performance profiling")
    
    def __init__(self, config_path: Optional[str] = None, **kwargs):
        """
        Initialize configuration from file and environment variables.
        
        Args:
            config_path: Path to YAML configuration file
            **kwargs: Additional configuration overrides
        """
        config_data = {}
        
        # Load from file if provided
        if config_path:
            config_data = self._load_from_file(config_path)
        else:
            # Try to load default config
            default_config_path = Path(__file__).parent.parent / "config" / "default.yaml"
            if default_config_path.exists():
                config_data = self._load_from_file(str(default_config_path))
        
        # Override with environment variables
        config_data.update(self._load_from_env())
        
        # Override with kwargs
        config_data.update(kwargs)
        
        super().__init__(**config_data)
    
    @staticmethod
    def _load_from_file(file_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            print(f"Warning: Could not load config file {file_path}: {e}")
            return {}
    
    @staticmethod
    def _load_from_env() -> Dict[str, Any]:
        """Load configuration from environment variables."""
        config = {}
        
        # Map environment variables to config structure
        env_mappings = {
            'FIFA_AI_DEBUG': ('debug_mode', bool),
            'FIFA_AI_LOG_LEVEL': ('logging.level', str),
            'FIFA_AI_CAPTURE_FPS': ('vision.capture_fps', int),
            'FIFA_AI_TEAM': ('game.preferred_team', str),
            'FIFA_AI_DASHBOARD_PORT': ('visualization.port', int),
        }
        
        for env_var, (config_path, config_type) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    # Convert value to appropriate type
                    if config_type == bool:
                        value = value.lower() in ('true', '1', 'yes', 'on')
                    elif config_type == int:
                        value = int(value)
                    elif config_type == float:
                        value = float(value)
                    
                    # Set nested config value
                    keys = config_path.split('.')
                    current = config
                    for key in keys[:-1]:
                        if key not in current:
                            current[key] = {}
                        current = current[key]
                    current[keys[-1]] = value
                    
                except (ValueError, TypeError) as e:
                    print(f"Warning: Invalid environment variable {env_var}={value}: {e}")
        
        return config
    
    def save(self, file_path: str) -> None:
        """Save current configuration to YAML file."""
        config_dict = self.dict()
        
        with open(file_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)
    
    @validator('vision')
    def validate_vision_config(cls, v):
        """Validate vision configuration."""
        if v.capture_fps <= 0:
            raise ValueError("Capture FPS must be positive")
        if not (0.0 <= v.detection_confidence <= 1.0):
            raise ValueError("Detection confidence must be between 0 and 1")
        return v
    
    @validator('ai')
    def validate_ai_config(cls, v):
        """Validate AI configuration."""
        if v.strategy_mode not in ['aggressive', 'defensive', 'adaptive']:
            raise ValueError("Strategy mode must be one of: aggressive, defensive, adaptive")
        if v.decision_frequency <= 0:
            raise ValueError("Decision frequency must be positive")
        return v
