#!/usr/bin/env python3
"""
FIFA 23 AI Agent - Main Application Launcher

Professional-grade AI agent for autonomous FIFA 23 gameplay.
Orchestrates all components including vision, AI decision-making, and input control.

Author: FIFA AI Team
Version: 1.0.0
License: MIT
"""

import asyncio
import signal
import sys
import time
from pathlib import Path
from typing import Optional

import click
from loguru import logger
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from core.match_manager import MatchManager
from utils.config import Config
from utils.logger import setup_logging
from utils.performance import PerformanceMonitor
from visualization.dashboard import Dashboard


class FIFAAIAgent:
    """
    Main FIFA AI Agent orchestrator.
    
    Coordinates all subsystems including computer vision, AI decision-making,
    input control, and performance monitoring for autonomous FIFA 23 gameplay.
    """
    
    def __init__(self, config_path: Optional[str] = None) -> None:
        """Initialize the FIFA AI Agent with configuration."""
        self.console = Console()
        self.config = Config(config_path)
        self.performance_monitor = PerformanceMonitor()
        self.match_manager: Optional[MatchManager] = None
        self.dashboard: Optional[Dashboard] = None
        self._running = False
        
        # Setup logging
        setup_logging(self.config.logging)
        
        logger.info("FIFA AI Agent initialized")
        
    async def initialize(self) -> bool:
        """
        Initialize all subsystems and verify FIFA 23 is ready.
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            self.console.print(Panel.fit(
                "[bold blue]🏆 FIFA 23 AI Agent[/bold blue]\n"
                "[dim]Professional Real-Time Gaming AI[/dim]",
                border_style="blue"
            ))
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console,
            ) as progress:
                
                # Initialize match manager
                task1 = progress.add_task("Initializing match manager...", total=None)
                self.match_manager = MatchManager(self.config)
                await self.match_manager.initialize()
                progress.update(task1, completed=True)
                
                # Initialize dashboard if enabled
                if self.config.visualization.enabled:
                    task2 = progress.add_task("Starting visualization dashboard...", total=None)
                    self.dashboard = Dashboard(self.config)
                    await self.dashboard.initialize()
                    progress.update(task2, completed=True)
                
                # Verify FIFA 23 is running
                task3 = progress.add_task("Detecting FIFA 23...", total=None)
                if not await self.match_manager.detect_fifa():
                    self.console.print("[red]❌ FIFA 23 not detected. Please start FIFA 23 and try again.[/red]")
                    return False
                progress.update(task3, completed=True)
                
                # Start performance monitoring
                task4 = progress.add_task("Starting performance monitoring...", total=None)
                self.performance_monitor.start()
                progress.update(task4, completed=True)
            
            self.console.print("[green]✅ All systems initialized successfully![/green]")
            return True
            
        except Exception as e:
            logger.error(f"Initialization failed: {e}")
            self.console.print(f"[red]❌ Initialization failed: {e}[/red]")
            return False
    
    async def run(self) -> None:
        """Main execution loop for the FIFA AI Agent."""
        if not await self.initialize():
            return
        
        self._running = True
        
        try:
            self.console.print("\n[bold green]🚀 FIFA AI Agent is now active![/bold green]")
            self.console.print("[dim]The agent will automatically navigate to Quick Match and start playing.[/dim]\n")
            
            # Main game loop
            while self._running:
                try:
                    # Check if FIFA is still running
                    if not await self.match_manager.detect_fifa():
                        logger.warning("FIFA 23 no longer detected")
                        break
                    
                    # Execute one game cycle
                    await self.match_manager.run_cycle()
                    
                    # Update dashboard if enabled
                    if self.dashboard:
                        await self.dashboard.update()
                    
                    # Small delay to prevent excessive CPU usage
                    await asyncio.sleep(0.01)  # 100 FPS max
                    
                except KeyboardInterrupt:
                    logger.info("Received interrupt signal")
                    break
                except Exception as e:
                    logger.error(f"Error in main loop: {e}")
                    await asyncio.sleep(1)  # Brief pause before retry
        
        finally:
            await self.shutdown()
    
    async def shutdown(self) -> None:
        """Gracefully shutdown all subsystems."""
        self._running = False
        
        self.console.print("\n[yellow]🛑 Shutting down FIFA AI Agent...[/yellow]")
        
        try:
            # Stop performance monitoring
            if self.performance_monitor:
                self.performance_monitor.stop()
                self.performance_monitor.save_report()
            
            # Shutdown match manager
            if self.match_manager:
                await self.match_manager.shutdown()
            
            # Shutdown dashboard
            if self.dashboard:
                await self.dashboard.shutdown()
            
            self.console.print("[green]✅ Shutdown complete[/green]")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    def handle_signal(self, signum: int, frame) -> None:
        """Handle system signals for graceful shutdown."""
        logger.info(f"Received signal {signum}")
        self._running = False


@click.command()
@click.option(
    "--config",
    "-c",
    type=click.Path(exists=True),
    help="Path to configuration file"
)
@click.option(
    "--debug",
    "-d",
    is_flag=True,
    help="Enable debug logging"
)
@click.option(
    "--no-dashboard",
    is_flag=True,
    help="Disable visualization dashboard"
)
def main(config: Optional[str], debug: bool, no_dashboard: bool) -> None:
    """
    Launch the FIFA 23 AI Agent.
    
    A professional AI agent that can autonomously play FIFA 23 in real-time
    using computer vision, reinforcement learning, and intelligent strategy.
    """
    # Setup signal handlers
    agent = FIFAAIAgent(config)
    signal.signal(signal.SIGINT, agent.handle_signal)
    signal.signal(signal.SIGTERM, agent.handle_signal)
    
    # Override config with CLI options
    if debug:
        agent.config.logging.level = "DEBUG"
    if no_dashboard:
        agent.config.visualization.enabled = False
    
    try:
        # Run the agent
        asyncio.run(agent.run())
    except KeyboardInterrupt:
        pass
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
